{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 12147485347500963869], [16702348383442838006, "build_script_build", false, 8400732139896795745], [15180317924524316486, "build_script_build", false, 4998191462341224797]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri_app_vue-2484da897ab8be90\\output", "paths": ["wrapper.hpp", "tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}