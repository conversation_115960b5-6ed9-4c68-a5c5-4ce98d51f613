D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\deps\tauri_app_vue_lib.lib: src\lib.rs src\ffi_utils.rs D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\build\tauri_app_vue-2484da897ab8be90\out/bindings.rs src\md_impl.rs src\trade_impl.rs

D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\deps\tauri_app_vue_lib.dll: src\lib.rs src\ffi_utils.rs D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\build\tauri_app_vue-2484da897ab8be90\out/bindings.rs src\md_impl.rs src\trade_impl.rs

D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\deps\libtauri_app_vue_lib.rlib: src\lib.rs src\ffi_utils.rs D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\build\tauri_app_vue-2484da897ab8be90\out/bindings.rs src\md_impl.rs src\trade_impl.rs

D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\deps\tauri_app_vue_lib.d: src\lib.rs src\ffi_utils.rs D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\build\tauri_app_vue-2484da897ab8be90\out/bindings.rs src\md_impl.rs src\trade_impl.rs

src\lib.rs:
src\ffi_utils.rs:
D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\build\tauri_app_vue-2484da897ab8be90\out/bindings.rs:
src\md_impl.rs:
src\trade_impl.rs:

# env-dep:OUT_DIR=D:\\personalWork\\tauri\\demo2\\tauri_app_vue\\src-tauri\\target\\debug\\build\\tauri_app_vue-2484da897ab8be90\\out
