// 交易面板窗口管理组合式函数

import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { WebviewWindow } from '@tauri-apps/api/webviewWindow'

export function useTradingPanels() {
  // 交易面板窗口管理
  const tradingPanels = ref<Map<string, WebviewWindow>>(new Map())

  // 检查指定集合是否有活跃的窗口
  const hasActivePanels = (setNumber: number): boolean => {
    return Array.from(tradingPanels.value.keys())
      .some(id => id.startsWith(`trading-panel-${setNumber}-`))
  }

  // 获取指定集合的所有窗口
  const getSetPanels = (setNumber: number): [string, WebviewWindow][] => {
    return Array.from(tradingPanels.value.entries())
      .filter(([id]) => id.startsWith(`trading-panel-${setNumber}-`))
  }

  // 打开交易面板
  const openTradingPanel = async (setNumber: number, tradeName?: string, contractCode?: string) => {
    try {
      // 每次都创建新窗口，不检查现有面板
      console.log(`为集合${setNumber}创建新的交易面板: ${tradeName}`)

      // 创建新窗口
      const timestamp = Date.now()
      const panelId = `trading-panel-${setNumber}-${timestamp}`

      // 立即添加到管理列表，防止重复创建
      tradingPanels.value.set(panelId, null as any) // 临时占位

      // 计算窗口位置
      const offsetX = (setNumber - 1) * 50 // 根据集合编号偏移
      const offsetY = (setNumber - 1) * 50

      // 构建URL，如果有合约代码则通过查询参数传递
      let url = '#/trading-panel'
      if (contractCode) {
        url += `?contract=${encodeURIComponent(contractCode)}`
      }

      // 创建新的交易面板窗口
      const panel = new WebviewWindow(panelId, {
        url,
        title: tradeName,
        width: 300,
        height: 800,
        resizable: true,
        decorations: false,
        alwaysOnTop: false,
        center: false,
        x: 300 + offsetX,
        y: 150 + offsetY,
        skipTaskbar: false,
        visible: true
      })

      // 监听窗口创建成功
      panel.once('tauri://created', () => {
        tradingPanels.value.set(panelId, panel) // 替换临时占位
        console.log(`交易面板创建成功: ${panelId}`)
      })

      // 监听窗口关闭
      panel.once('tauri://close-requested', () => {
        tradingPanels.value.delete(panelId)
        console.log(`交易面板已关闭: ${panelId}`)
      })

      // 监听窗口销毁（确保清理）
      panel.once('tauri://destroyed', () => {
        if (tradingPanels.value.has(panelId)) {
          tradingPanels.value.delete(panelId)
          console.log(`交易面板已销毁并清理: ${panelId}`)
        }
      })

      // 监听窗口创建失败
      panel.once('tauri://error', (error) => {
        console.error('创建交易面板失败:', error)
        message.error(`创建交易面板失败: ${error}`)
        // 清理临时占位
        tradingPanels.value.delete(panelId)
      })

    } catch (error) {
      console.error('打开交易面板失败:', error)
      message.error(`打开交易面板失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 关闭指定交易集的面板
  const closeAllPanels = async (setNumber: number) => {
    try {
      const panelsToClose = getSetPanels(setNumber)

      if (panelsToClose.length === 0) {
        message.info(`交易集${setNumber}没有打开的面板`)
        return
      }

      const [id, panel] = panelsToClose[0] // 现在每个集合只有一个面板
      await panel.close()
      tradingPanels.value.delete(id)

    } catch (error) {
      console.error('关闭面板失败:', error)
      message.error('关闭面板失败')
    }
  }

  // 关闭所有面板
  const closeAllTradingPanels = async () => {
    try {
      const panelEntries = Array.from(tradingPanels.value.entries())
      console.log(`准备关闭 ${panelEntries.length} 个交易面板`)

      for (const [panelId, panel] of panelEntries) {
        try {
          // 检查面板是否有效（不是临时占位符）
          if (panel && typeof panel.close === 'function') {
            console.log(`关闭交易面板: ${panelId}`)
            await panel.close()
          } else {
            console.log(`跳过无效面板: ${panelId}`)
          }
        } catch (error) {
          console.error(`关闭面板 ${panelId} 失败:`, error)
          // 继续关闭其他面板，不因单个面板失败而中断
        }
      }

      // 清空面板映射
      tradingPanels.value.clear()
      console.log('所有交易面板关闭完成')
    } catch (error) {
      console.error('关闭所有面板失败:', error)
    }
  }

  return {
    tradingPanels,
    hasActivePanels,
    getSetPanels,
    openTradingPanel,
    closeAllPanels,
    closeAllTradingPanels
  }
}
